'use client'

import { useEffect, useRef, useState } from 'react'
import { useQueue } from '@/hooks/useQueue'
import { YouTubePlayer } from '@/lib/types/video'

declare global {
  interface Window {
    YT: any
    onYouTubeIframeAPIReady: () => void
    youtubePlayer: YouTubePlayer | null
  }
}

export function VideoPlayer() {
  const {
    currentVideo,
    isPlaying,
    nextVideo,
    setPlaying,
    items,
    currentIndex,
    isLooping
  } = useQueue()
  const playerRef = useRef<HTMLDivElement>(null)
  const [player, setPlayer] = useState<YouTubePlayer | null>(null)
  const [isPlayerReady, setIsPlayerReady] = useState(false)
  const [isPlayerInstanceReady, setIsPlayerInstanceReady] = useState(false)

  // Initialize YouTube API
  useEffect(() => {
    if (typeof window !== 'undefined' && !window.YT) {
      console.log('📺 Loading YouTube IFrame API...')

      // Set up the callback for when API is ready
      window.onYouTubeIframeAPIReady = () => {
        console.log('✅ YouTube IFrame API ready')
        setIsPlayerReady(true)
      }

      // Load the API
      const tag = document.createElement('script')
      tag.src = 'https://www.youtube.com/iframe_api'
      const firstScriptTag = document.getElementsByTagName('script')[0]
      firstScriptTag.parentNode?.insertBefore(tag, firstScriptTag)
    } else if (window.YT) {
      setIsPlayerReady(true)
    }
  }, [])

  // Create player when API is ready and we have a video
  useEffect(() => {
    if (isPlayerReady && currentVideo && playerRef.current && !player) {
      console.log('🎵 Creating YouTube player for:', currentVideo.title)

      const newPlayer = new window.YT.Player(playerRef.current, {
        height: '100%',
        width: '100%',
        videoId: currentVideo.id,
        playerVars: {
          autoplay: 1,
          controls: 1,
          rel: 0,
          modestbranding: 1,
          fs: 1,
          cc_load_policy: 0,
          iv_load_policy: 3,
          autohide: 0
        },
        events: {
          onReady: (event: any) => {
            console.log('✅ YouTube player ready')
            setIsPlayerInstanceReady(true)
            event.target.playVideo()
            setPlaying(true)
          },
          onStateChange: (event: any) => {
            const state = event.data
            console.log('🎵 Player state changed:', state)

            // YouTube player states:
            // -1 (unstarted), 0 (ended), 1 (playing), 2 (paused), 3 (buffering), 5 (cued)

            if (state === 1) { // Playing
              setPlaying(true)
            } else if (state === 2) { // Paused
              setPlaying(false)
            } else if (state === 0) { // Ended
              setPlaying(false)
              handleVideoEnded()
            }
          },
          onError: (event: any) => {
            console.error('❌ YouTube player error:', event.data)
            // Try to skip to next video on error
            handleVideoEnded()
          }
        }
      })

      setPlayer(newPlayer)
      window.youtubePlayer = newPlayer
    }
  }, [isPlayerReady, currentVideo, player])

  // Reset player instance ready state when player changes
  useEffect(() => {
    if (!player) {
      setIsPlayerInstanceReady(false)
    }
  }, [player])

  // Handle video ended - move to next video
  const handleVideoEnded = () => {
    console.log('🔄 Video ended, moving to next...')

    if (items.length > 0) {
      const nextIndex = currentIndex + 1

      if (nextIndex < items.length) {
        // Play next video
        nextVideo()
      } else if (isLooping) {
        // Loop back to first video
        nextVideo() // This should handle looping in the queue provider
      } else {
        // Queue finished, stop playing
        setPlaying(false)
        console.log('🏁 Queue finished')
      }
    }
  }

  // Update player when current video changes
  useEffect(() => {
    if (player && isPlayerInstanceReady) {
      if (currentVideo && currentVideo.id) {
        // Load new video
        console.log('🔄 Loading new video:', currentVideo.title)
        try {
          player.loadVideoById(currentVideo.id)
        } catch (error) {
          console.error('❌ Error loading video:', error)
          console.log('Player object:', player)
          console.log('Available methods:', Object.getOwnPropertyNames(player))
        }
      } else {
        // No current video (queue cleared) - stop player and clear video
        console.log('🛑 No current video, stopping player')
        try {
          player.stopVideo()
          // Note: Don't call setPlaying(false) here as CLEAR_QUEUE already sets isPlaying: false
        } catch (error) {
          console.error('❌ Error stopping player:', error)
        }
      }
    }
  }, [player, isPlayerInstanceReady, currentVideo])

  // Control player based on isPlaying state
  useEffect(() => {
    if (player && isPlayerInstanceReady && typeof player.getPlayerState === 'function') {
      try {
        const playerState = player.getPlayerState()

        if (isPlaying && playerState !== 1) { // Not playing
          player.playVideo()
        } else if (!isPlaying && playerState === 1) { // Currently playing
          player.pauseVideo()
        }
      } catch (error) {
        console.error('❌ Error controlling player:', error)
      }
    }
  }, [player, isPlayerInstanceReady, isPlaying])

  return (
    <div className="glassmorphism rounded-2xl overflow-hidden">
      <div className="aspect-video bg-black relative">
        {currentVideo ? (
          <div
            ref={playerRef}
            className="w-full h-full"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center text-dark-400">
            <div className="text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-dark-700 rounded-full flex items-center justify-center">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M8 5v14l11-7z"/>
                </svg>
              </div>
              <p className="text-lg font-medium">No video selected</p>
              <p className="text-sm">Add videos to your queue to start playing</p>
            </div>
          </div>
        )}
      </div>

      {currentVideo && (
        <div className="p-4 border-t border-white/10">
          <h3 className="font-medium text-white truncate mb-1">
            {currentVideo.title}
          </h3>
          <p className="text-sm text-dark-300 truncate">
            {currentVideo.channel || 'Unknown Channel'}
          </p>
        </div>
      )}
    </div>
  )
}
